'use client';

import { Button } from 'antd';
import Image from 'next/image';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';

const UserHeader = () => {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href={ROUTES.HOME} className="flex items-center">
              <Image
                src="https://app-biziclick.tourvisio.com/assets/img/logo-biziclick-light-bg.svg"
                alt="BiziClick Logo"
                width={140}
                height={40}
                className="h-8 w-auto"
                priority
              />
            </Link>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link 
              href="/flights" 
              className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
            >
              Uçak <PERSON>ileti
            </Link>
            <Link 
              href="/hotels" 
              className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
            >
              Otel
            </Link>
            <Link 
              href="/cars" 
              className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
            >
              Araç Kiralama
            </Link>
            <Link 
              href="/packages" 
              className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
            >
              Paket Turlar
            </Link>
          </nav>

          {/* User Actions */}
          <div className="flex items-center space-x-4">
            <Link href={ROUTES.AUTH.LOGIN}>
              <Button type="default" size="middle">
                Giriş Yap
              </Button>
            </Link>
            <Link href={ROUTES.AUTH.REGISTER}>
              <Button type="primary" size="middle">
                Kayıt Ol
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default UserHeader;
