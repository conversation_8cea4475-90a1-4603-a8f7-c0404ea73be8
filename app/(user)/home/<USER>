'use client';

import { <PERSON>ton, Card, Checkbox, DatePicker, Form, Radio, Select } from 'antd';
import { useState } from 'react';
import { SwapOutlined, SearchOutlined } from '@ant-design/icons';
import CitySelect from '@/components/modules/selectbox/CitySelect';
import dayjs from 'dayjs';

interface FlightSearchData {
  tripType: 'oneWay' | 'roundTrip' | 'multiCity';
  from: string;
  to: string;
  departureDate: dayjs.Dayjs;
  returnDate?: dayjs.Dayjs;
  travelReason: 'business' | 'personal';
  cabinClass: 'economy' | 'business' | 'first';
  passenger: string;
  directFlightsOnly: boolean;
  includeBaggageFree: boolean;
}

const HomePage = () => {
  const [form] = Form.useForm<FlightSearchData>();
  const [tripType, setTripType] = useState<'oneWay' | 'roundTrip' | 'multiCity'>('roundTrip');

  const handleTripTypeChange = (value: 'oneWay' | 'roundTrip' | 'multiCity') => {
    setTripType(value);
    if (value === 'oneWay') {
      form.setFieldValue('returnDate', undefined);
    }
  };

  const handleSwapCities = () => {
    const fromValue = form.getFieldValue('from');
    const toValue = form.getFieldValue('to');

    form.setFieldsValue({
      from: toValue,
      to: fromValue,
    });
  };

  const handleSearch = (values: FlightSearchData) => {
    console.log('Flight search:', values);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <Card className="shadow-lg">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSearch}
            initialValues={{
              tripType: 'roundTrip',
              travelReason: 'business',
              cabinClass: 'economy',
              directFlightsOnly: false,
              includeBaggageFree: false,
            }}
          >
            {/* Trip Type Selection */}
            <div className="mb-6">
              <Radio.Group
                value={tripType}
                onChange={(e) => handleTripTypeChange(e.target.value)}
                className="flex gap-8"
              >
                <Radio value="oneWay" className="text-base">
                  ✈️ Tek Yön
                </Radio>
                <Radio value="roundTrip" className="text-base">
                  🔄 Gidiş-Dönüş
                </Radio>
                <Radio value="multiCity" className="text-base">
                  🗺️ Çoklu Uçuş
                </Radio>
              </Radio.Group>
            </div>

            {/* Main Search Fields */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 mb-6">
              {/* From City */}
              <div className="lg:col-span-3">
                <Form.Item
                  name="from"
                  label="Nereden"
                  rules={[{ required: true, message: 'Kalkış şehri seçiniz' }]}
                >
                  <CitySelect
                    placeholder="Şehir ya da havaalanı adı giriniz"
                    size="large"
                  />
                </Form.Item>
              </div>

              {/* Swap Button */}
              <div className="lg:col-span-1 flex items-end justify-center pb-2">
                <Button
                  type="text"
                  icon={<SwapOutlined />}
                  onClick={handleSwapCities}
                  className="h-10 w-10 flex items-center justify-center text-blue-600 hover:bg-blue-50 rounded-full"
                />
              </div>

              {/* To City */}
              <div className="lg:col-span-3">
                <Form.Item
                  name="to"
                  label="Nereye"
                  rules={[{ required: true, message: 'Varış şehri seçiniz' }]}
                >
                  <CitySelect
                    placeholder="Şehir ya da havaalanı adı giriniz"
                    size="large"
                  />
                </Form.Item>
              </div>

              {/* Departure Date */}
              <div className="lg:col-span-2">
                <Form.Item
                  name="departureDate"
                  label="Seyahat Tarihi"
                  rules={[{ required: true, message: 'Gidiş tarihi seçiniz' }]}
                >
                  <DatePicker
                    size="large"
                    placeholder="2025-07-31"
                    format="YYYY-MM-DD"
                    className="w-full"
                    disabledDate={(current) => current && current < dayjs().startOf('day')}
                  />
                </Form.Item>
              </div>

              {/* Return Date */}
              {tripType === 'roundTrip' && (
                <div className="lg:col-span-2">
                  <Form.Item
                    name="returnDate"
                    label="Dönüş Tarihi"
                    rules={[
                      { required: tripType === 'roundTrip', message: 'Dönüş tarihi seçiniz' }
                    ]}
                  >
                    <DatePicker
                      size="large"
                      placeholder="Dönüş tarihi"
                      format="YYYY-MM-DD"
                      className="w-full"
                      disabledDate={(current) => {
                        const departureDate = form.getFieldValue('departureDate');
                        return current && (
                          current < dayjs().startOf('day') ||
                          (departureDate && current < departureDate.startOf('day'))
                        );
                      }}
                    />
                  </Form.Item>
                </div>
              )}

              {/* Search Button */}
              <div className={`${tripType === 'roundTrip' ? 'lg:col-span-1' : 'lg:col-span-3'} flex items-end pb-2`}>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  icon={<SearchOutlined />}
                  className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg"
                >
                  Ara
                </Button>
              </div>
            </div>

            {/* Additional Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {/* Travel Reason */}
              <Form.Item name="travelReason" label="Seyahat Nedeni">
                <Select size="large" placeholder="Seyahat nedeni seçiniz">
                  <Select.Option value="business">İş</Select.Option>
                  <Select.Option value="personal">Kişisel</Select.Option>
                </Select>
              </Form.Item>

              {/* Cabin Class */}
              <Form.Item name="cabinClass" label="Kabin Sınıfı, Yolcular">
                <Select size="large" placeholder="Kabin sınıfı seçiniz">
                  <Select.Option value="economy">1 Yolcu, Ekonomi</Select.Option>
                  <Select.Option value="business">1 Yolcu, Business</Select.Option>
                  <Select.Option value="first">1 Yolcu, First Class</Select.Option>
                </Select>
              </Form.Item>

              {/* User Selection */}
              <Form.Item name="passenger" label="Kullanıcı Seçimi">
                <Select size="large" placeholder="Kullanıcı Seçiniz">
                  <Select.Option value="self">Kendim için</Select.Option>
                  <Select.Option value="other">Başkası için</Select.Option>
                </Select>
              </Form.Item>

              {/* Empty column for spacing */}
              <div></div>
            </div>

            {/* Checkboxes */}
            <div className="flex flex-wrap gap-6">
              <Form.Item name="directFlightsOnly" valuePropName="checked">
                <Checkbox>Sadece direkt uçuşlar</Checkbox>
              </Form.Item>
              <Form.Item name="includeBaggageFree" valuePropName="checked">
                <Checkbox>Bagajsız uçuşları da göster</Checkbox>
              </Form.Item>
            </div>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default HomePage;
